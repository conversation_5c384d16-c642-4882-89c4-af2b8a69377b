import 'package:flutter/material.dart';
import 'package:better_player/better_player.dart';
import '../../models/preset_item.dart';
import '../controllers/custom_player_controller.dart';
import '../themes/player_theme.dart';
import '../widgets/menus/fit_mode_selection_menu.dart';
import 'custom_controls.dart';

class VideoPlayerWidget extends StatefulWidget {
  final PresetItem videoItem;

  const VideoPlayerWidget({
    super.key,
    required this.videoItem,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late BetterPlayerController _betterPlayerController;
  late CustomPlayerController _customController;
  int _currentSourceIndex = 0;
  bool _isRetrying = false;

  @override
  void initState() {
    super.initState();
    _customController = CustomPlayerController();
    _setupPlayer();
    _startPipStatusMonitoring();
  }

  void _startPipStatusMonitoring() {
    // Periodically update PiP status
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _customController.updatePipStatus();
        _startPipStatusMonitoring();
      }
    });
  }

  void _setupPlayer() {
    _setupPlayerWithSource(_currentSourceIndex);
  }

  void _setupPlayerWithSource(int sourceIndex) {
    if (sourceIndex >= widget.videoItem.sources.length) {
      // No more sources to try
      debugPrint('No more sources to try');
      return;
    }

    _currentSourceIndex = sourceIndex;

    final source = widget.videoItem.sources[sourceIndex];
    final isNetwork = source.url.startsWith('http');
    final isHLS = source.url.contains('.m3u8');

    final dataSource = BetterPlayerDataSource(
      isNetwork
          ? BetterPlayerDataSourceType.network
          : BetterPlayerDataSourceType.file,
      source.url,
      liveStream: isHLS,
      headers: {
        if (source.userAgent != null) 'User-Agent': source.userAgent!,
        if (source.referer != null) 'Referer': source.referer!,
      },
    );

    _betterPlayerController = BetterPlayerController(
      BetterPlayerConfiguration(
        autoPlay: true,
        looping: false,
        aspectRatio: 16 / 9,
        // Force video renderer refresh for source switching
        allowedScreenSleep: false,
        // Disable default controls since we're using custom ones
        controlsConfiguration: const BetterPlayerControlsConfiguration(
          showControls: false,
        ),
        // Custom fullscreen page builder to include our controls
        routePageBuilder:
            (context, animation, secondaryAnimation, controllerProvider) {
          return Scaffold(
            backgroundColor: Colors.black,
            body: Stack(
              children: [
                // Video player with fit mode
                Positioned.fill(
                  child: AnimatedBuilder(
                    animation: _customController,
                    builder: (context, child) {
                      return FittedBox(
                        fit: _customController.fitMode.boxFit,
                        child: controllerProvider,
                      );
                    },
                  ),
                ),
                // Our custom controls overlay
                Positioned.fill(
                  child: CustomControls(
                    controller: _customController,
                    title: widget.videoItem.name,
                    showTitle: true,
                    videoItem: widget.videoItem,
                    currentSourceIndex: _currentSourceIndex,
                    onSourceSelected: switchToSource,
                  ),
                ),
              ],
            ),
          );
        },
        // Custom error handling through our controller
        errorBuilder: (context, errorMessage) {
          return Container(
            color: PlayerTheme.backgroundColor,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline_rounded,
                    color: PlayerTheme.errorColor,
                    size: 64,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load video',
                    style: PlayerTheme.errorTextStyle,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    errorMessage ?? 'Unknown error occurred',
                    style: PlayerTheme.subtitleTextStyle,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
      betterPlayerDataSource: dataSource,
    );

    // Initialize our custom controller with the BetterPlayer controller
    _customController.initialize(_betterPlayerController);

    // Listen to player events for error handling and automatic failover
    _betterPlayerController.addEventsListener((event) {
      if (event.betterPlayerEventType == BetterPlayerEventType.exception) {
        _handlePlaybackError();
      }
    });

    // Automatically enter fullscreen mode after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _betterPlayerController.enterFullScreen();
      }
    });
  }

  /// Handle playback errors and try next source
  void _handlePlaybackError() {
    if (_isRetrying) return; // Prevent multiple retries

    _isRetrying = true;
    final nextSourceIndex = _currentSourceIndex + 1;

    if (nextSourceIndex < widget.videoItem.sources.length) {
      debugPrint('Switching to source ${nextSourceIndex + 1} due to error');

      // Dispose current controller
      _betterPlayerController.dispose();

      // Setup with next source
      _setupPlayerWithSource(nextSourceIndex);

      // Reset retry flag after a delay
      Future.delayed(const Duration(seconds: 2), () {
        _isRetrying = false;
      });
    } else {
      debugPrint('No more sources available');
      _isRetrying = false;
    }
  }

  /// Switch to a specific source manually
  void switchToSource(int sourceIndex) {
    if (sourceIndex >= 0 &&
        sourceIndex < widget.videoItem.sources.length &&
        sourceIndex != _currentSourceIndex) {
      debugPrint('Manually switching to source ${sourceIndex + 1}');

      // Store current playing state
      final wasPlaying = _betterPlayerController.isPlaying() ?? false;

      // Dispose current controller and set up with the new source
      _betterPlayerController.dispose();
      _setupPlayerWithSource(sourceIndex);

      // Force widget rebuild
      if (mounted) {
        setState(() {});
      }

      // Restore playback state after a delay
      _restorePlaybackState(wasPlaying);
    }
  }

  /// Restore playback state after source switching
  void _restorePlaybackState(bool wasPlaying) {
    // Wait for the new player to initialize
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (!mounted) return;

      // Resume playback if it was playing before
      if (wasPlaying) {
        _betterPlayerController.play();
      }
    });
  }

  @override
  void dispose() {
    _customController.dispose();
    _betterPlayerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: PlayerTheme.backgroundColor,
      child: Stack(
        children: [
          // Single video player instance with fit mode
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _customController,
              builder: (context, child) {
                return FittedBox(
                  fit: _customController.fitMode.boxFit,
                  child: BetterPlayer(
                    key: ValueKey('source_$_currentSourceIndex'),
                    controller: _betterPlayerController,
                  ),
                );
              },
            ),
          ),
          // Controls that switch based on PiP status
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _customController,
              builder: (context, child) {
                // Simple approach - just show different controls
                // without using PiPSwitcher to avoid disposal issues
                return _customController.isPipMode
                    ? _buildPipControls()
                    : _buildFullControls();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullControls() {
    return CustomControls(
      controller: _customController,
      title: widget.videoItem.name,
      showTitle: true,
      videoItem: widget.videoItem,
      currentSourceIndex: _currentSourceIndex,
      onSourceSelected: switchToSource,
      onFullscreenPressed: () {
        _betterPlayerController.toggleFullScreen();
      },
    );
  }

  Widget _buildPipControls() {
    return AnimatedBuilder(
      animation: _customController,
      builder: (context, child) {
        return GestureDetector(
          onTap: _customController.togglePlayPause,
          behavior: HitTestBehavior.opaque,
          child: Container(
            width: double.infinity,
            height: double.infinity,
            child: Stack(
              children: [
                // Center play/pause button (only show when paused or buffering)
                if (!_customController.isPlaying ||
                    _customController.isBuffering)
                  Center(
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: PlayerTheme.controlBackgroundActive,
                        shape: BoxShape.circle,
                        boxShadow: PlayerTheme.buttonShadow,
                      ),
                      child: IconButton(
                        icon: Icon(
                          _customController.isBuffering
                              ? Icons.hourglass_empty_rounded
                              : _customController.isPlaying
                                  ? PlayerTheme.pauseIcon
                                  : PlayerTheme.playIcon,
                        ),
                        color: PlayerTheme.buttonColor,
                        iconSize: 24,
                        onPressed: _customController.isBuffering
                            ? null
                            : _customController.togglePlayPause,
                      ),
                    ),
                  ),

                // Loading indicator
                if (_customController.isBuffering)
                  const Center(
                    child: CircularProgressIndicator(
                      color: PlayerTheme.primaryColor,
                      strokeWidth: 2,
                    ),
                  ),

                // Error overlay
                if (_customController.hasError)
                  Container(
                    color: PlayerTheme.overlayColor,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error_outline_rounded,
                            color: PlayerTheme.errorColor,
                            size: 32,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Error',
                            style: PlayerTheme.errorTextStyle
                                .copyWith(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
