import 'package:flutter/material.dart';
import '../../controllers/custom_player_controller.dart';
import '../../themes/player_theme.dart';

/// Enum for video fit modes
enum VideoFitMode {
  contain,
  cover,
  fill,
  fitWidth,
  fitHeight,
  scaleDown,
}

/// Extension to get display names and icons for fit modes
extension VideoFitModeExtension on VideoFitMode {
  String get displayName {
    switch (this) {
      case VideoFitMode.contain:
        return 'Fit';
      case VideoFitMode.cover:
        return 'Cover';
      case VideoFitMode.fill:
        return 'Fill';
      case VideoFitMode.fitWidth:
        return 'Fit Width';
      case VideoFitMode.fitHeight:
        return 'Fit Height';
      case VideoFitMode.scaleDown:
        return 'Scale Down';
    }
  }

  String get description {
    switch (this) {
      case VideoFitMode.contain:
        return 'Fit entire video within bounds';
      case VideoFitMode.cover:
        return 'Fill bounds, may crop video';
      case VideoFitMode.fill:
        return 'Stretch to fill bounds';
      case VideoFitMode.fitWidth:
        return 'Fit video width to bounds';
      case VideoFitMode.fitHeight:
        return 'Fit video height to bounds';
      case VideoFitMode.scaleDown:
        return 'Scale down if larger';
    }
  }

  IconData get icon {
    switch (this) {
      case VideoFitMode.contain:
        return Icons.fit_screen;
      case VideoFitMode.cover:
        return Icons.crop_free;
      case VideoFitMode.fill:
        return Icons.fullscreen;
      case VideoFitMode.fitWidth:
        return Icons.width_wide;
      case VideoFitMode.fitHeight:
        return Icons.height;
      case VideoFitMode.scaleDown:
        return Icons.compress;
    }
  }

  BoxFit get boxFit {
    switch (this) {
      case VideoFitMode.contain:
        return BoxFit.contain;
      case VideoFitMode.cover:
        return BoxFit.cover;
      case VideoFitMode.fill:
        return BoxFit.fill;
      case VideoFitMode.fitWidth:
        return BoxFit.fitWidth;
      case VideoFitMode.fitHeight:
        return BoxFit.fitHeight;
      case VideoFitMode.scaleDown:
        return BoxFit.scaleDown;
    }
  }
}

/// Menu widget for selecting video fit mode
class FitModeSelectionMenu extends StatelessWidget {
  final CustomPlayerController controller;

  const FitModeSelectionMenu({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return PopupMenuButton<VideoFitMode>(
          onSelected: controller.setFitMode,
          itemBuilder: (context) => VideoFitMode.values.map((fitMode) {
            return PopupMenuItem<VideoFitMode>(
              value: fitMode,
              child: Row(
                children: [
                  Icon(
                    fitMode.icon,
                    size: 20,
                    color: controller.fitMode == fitMode
                        ? PlayerTheme.primaryColor
                        : PlayerTheme.textSecondary,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          fitMode.displayName,
                          style: TextStyle(
                            color: controller.fitMode == fitMode
                                ? PlayerTheme.primaryColor
                                : PlayerTheme.textPrimary,
                            fontWeight: controller.fitMode == fitMode
                                ? FontWeight.w600
                                : FontWeight.normal,
                          ),
                        ),
                        Text(
                          fitMode.description,
                          style: TextStyle(
                            color: PlayerTheme.textSecondary,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (controller.fitMode == fitMode)
                    const Icon(
                      Icons.check,
                      size: 16,
                      color: PlayerTheme.primaryColor,
                    ),
                ],
              ),
            );
          }).toList(),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Icon(
              controller.fitMode.icon,
              color: PlayerTheme.buttonColor,
            ),
          ),
        );
      },
    );
  }
}
